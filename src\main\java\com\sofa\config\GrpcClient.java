package com.sofa.config;


import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.springframework.stereotype.Component;

@Component
public class GrpcClient {

    public String sayHello(String name) {
        ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 9090)
                .usePlaintext()
                .build();

        GreeterGrpc.GreeterBlockingStub stub = GreeterGrpc.newBlockingStub(channel);
        HelloProto.HelloReply response = stub.sayHello(
                HelloProto.HelloRequest.newBuilder()
                        .setName(name)
                        .build());

        channel.shutdown();
        return response.getMessage();
    }
}