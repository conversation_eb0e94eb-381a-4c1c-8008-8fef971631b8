package com.sofa.controller;


import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sofa.config.GrpcClient;

@RestController
public class DemoController {

    private final GrpcClient grpcClient;

    public DemoController(GrpcClient grpcClient) {
        this.grpcClient = grpcClient;
    }

    @GetMapping("/hello")
    public String sayHello(@RequestParam String name) {
        return grpcClient.sayHello(name);
    }
}
