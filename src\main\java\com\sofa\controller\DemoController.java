package com.sofa.controller;

import com.example.demo.client.GrpcClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DemoController {

    private final GrpcClient grpcClient;

    public DemoController(GrpcClient grpcClient) {
        this.grpcClient = grpcClient;
    }

    @GetMapping("/hello")
    public String sayHello(@RequestParam String name) {
        return grpcClient.sayHello(name);
    }
}
الخطوة 8: تشغيل التطبيق
قم بتشغيل Spring Boot Application

افتح المتصفح واذهب إلى:

text
http://localhost:8080/hello?name=YourName
يجب أن ترى الرد: "Hello, YourName!"

ملاحظات إضافية
تأكد من أن البورت 9090 غير مستخدم لخدمة gRPC

يمكنك استخدام أدوات مثل BloomRPC أو Postman (مع دعم gRPC) لاختبار الخدمة مباشرة

لإضافة أمان، يمكنك استخدام SSL/TLS لاتصالات gRPC

هذا المشروع البسيط يوضح كيفية إنشاء خدمة gRPC مع Spring Boot. يمكنك توسيعه بإضافة المزيد من الخدمات والوظائف حسب احتياجاتك.

New chat

