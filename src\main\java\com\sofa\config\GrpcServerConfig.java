package com.sofa.config;

import io.grpc.Server;
import io.grpc.ServerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class GrpcServerConfig {

    @Bean
    public Server grpcServer(GreeterService greeterService) throws IOException {
        Server server = ServerBuilder.forPort(9090)
                .addService(greeterService)
                .build();
        
        server.start();
        Runtime.getRuntime().addShutdownHook(new Thread(server::shutdown));
        return server;
    }
}