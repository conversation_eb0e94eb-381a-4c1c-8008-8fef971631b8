package com.sofa.demo.service;

import com.sofa.demo.grpc.GreeterGrpc;
import com.example.demo.grpc.HelloProto;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;

@GrpcService
public class GreeterService extends GreeterGrpc.GreeterImplBase {

    @Override
    public void sayHello(HelloProto.HelloRequest request, StreamObserver<HelloProto.HelloReply> responseObserver) {
        String message = "Hello, " + request.getName() + "!";
        HelloProto.HelloReply reply = HelloProto.HelloReply.newBuilder()
                .setMessage(message)
                .build();
        
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }
}
